const { Sequelize } = require('sequelize');

module.exports = (app) => {
    const sequelize = new Sequelize('taifaworkingcapital', 'dev', 'Ahadho13', {
        host: 'localhost',
        dialect: 'postgres',
        timezone:"+03:00",
        dialectOptions: {
            useUTC: false, // for reading from database
          },
        // logging: (...msg) => console.log(msg)
    });

    sequelize.authenticate().then(() => {
        console.log('Database connection has been established successfully.');
    }).catch(error => {
        console.error('Unable to connect to the database:', error);
    });

    app.set('sequelize', sequelize);
    app.set('sequelizeSync', sequelize.sync({alter: true}));
}
