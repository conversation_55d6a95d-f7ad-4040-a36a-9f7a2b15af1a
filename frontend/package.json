{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "cross-env  VUE_APP_STAGE=prod vue-cli-service build --dest ../public/dist", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "dev": "cross-env VUE_APP_STAGE=dev vue-cli-service serve"}, "dependencies": {"axios": "^0.27.2", "core-js": "^3.6.5", "cross-env": "^7.0.3", "vue-excel-export": "^0.1.3", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuetify": "^2.6.10", "vuex": "^3.4.0"}, "devDependencies": {"@nuxtjs/vuetify": "^1.12.3", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "^5.0.4", "@vue/cli-service": "~4.5.15", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.5.0", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0"}}