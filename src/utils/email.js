// require("dotenv").config();
// const sgMail = require("@sendgrid/mail");

// const apikey =
//   "*********************************************************************";

// sgMail.setApiKey(apikey);

// module.exports = sendEmail;

// async function sendEmail({
//   to,
//   subject,
//   from = "<EMAIL>",
 
//   ...rest
// }) {
//   try {
//     const msg = {
//       to,
//       subject,
//       from,
//       ...rest,
//     };

//     console.log({
//       apikey,
//       p: process.env.sendgrid_key,
//     });

//     await sgMail.send(msg);
//   } catch (error) {
//     console.log({ error }, "kwa mail");
//     throw error;
//   }
// }



const axios = require("axios");

const email = {
  loadApi: () => {
    return axios.create({
      baseURL: process.env.EMAIL_API_URL,
      timeout: 60000, // 60 seconds timeout
      headers: {
        'apiKey': process.env.EMAIL_API_KEY,
        'Content-Type': 'application/json'
      }
    });
  },
  sendEmail: async ({
    to,
    subject,
    emailBody,
    tenant = "platinumKenya",
    attachments = [],
    cc = [],
  }) => {
    const payload = {
      to,
      subject,
      body: emailBody,
      tenantId: tenant,
      groupRecordId: "pkeUserAccess",
      attachments,
    };

    // Add CC if provided
    if (cc && cc.length > 0) {
      payload.cc = cc;
    }

    console.log('Email API payload:', JSON.stringify({
      ...payload,
      attachments: payload.attachments.map(att => ({
        filename: att.filename,
        type: att.type,
        contentLength: att.content ? att.content.length : 0
      }))
    }, null, 2));

    try {
      const emailSent = await email.loadApi().post("/email", payload);
      console.log('Email sent successfully:', emailSent.status);
      return emailSent;
    } catch (error) {
      console.error('Email API Error:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
      throw error;
    }
  },
};

module.exports = email;
