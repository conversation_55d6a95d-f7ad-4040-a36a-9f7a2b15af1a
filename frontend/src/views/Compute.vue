<template>
  <v-card class="mx-auto my-auto" width="200px" flat>
    <v-btn to="/" color="primary" class="ma-auto" :disabled="disabled"> Back to Reports</v-btn>
  </v-card>
</template>

<script>
// @ is an alias to /src
import DemandLetterService from "@/services/proclamationdemand.service.js";
export default {
  name: "Compute",
  components: {},
  data: function () {
    return {
      disabled: true
    }
  },
  mounted() {
    DemandLetterService.demandLetter()
      .then(({ message }) => {
        this.$store.dispatch("toggleModal", message);
      })
      .catch(() => {
        console.log("err here");
        this.$store.dispatch(
          "toggleModal",
          "oops, something went wrong, retrying..."
        );
        return DemandLetterService.demandLetter();
      })
      .then(({ message }) => {
        this.$store.dispatch("toggleModal", message.message);
      }).finally(() => {
        this.disabled = false
      })
  },
};
</script>

