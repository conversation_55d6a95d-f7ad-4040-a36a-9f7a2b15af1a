// const path = require("path");

// module.exports = {
//   //Should be STATIC_URL + path/to/build
//   publicPath: "/src/views/",

//   // // Output to a directory in STATICFILES_DIRS
//   outputDir: path.resolve(__dirname, "../src/views/"),

//   // // Django will hash file names, not webpack
//   filenameHashing: false,

//   // // See: https://vuejs.org/v2/guide/installation.html#Runtime-Compiler-vs-Runtime-only
//   runtimeCompiler: true,

//   // devServer: {
//   //   writeToDisk: true, // Write files to disk in dev mode, so Django can serve the assets
//   // },
//   transpileDependencies: ["vuetify"],
// };

const path = require("path");

const crypto = require("crypto");

/**
 * The MD4 algorithm is not available anymore in Node.js 17+ (because of library SSL 3).
 * In that case, silently replace MD4 by the MD5 algorithm.
 */
try {
  crypto.createHash("md4");
} catch (e) {
  console.warn('Crypto "MD4" is not supported anymore by this Node.js version');
  const origCreateHash = crypto.createHash;
  crypto.createHash = (alg, opts) => {
    return origCreateHash(alg === "md4" ? "md5" : alg, opts);
  };
}


module.exports = {
  // Should be STATIC_URL + path/to/build
  publicPath: "./",

  // Output to a directory in STATICFILES_DIRS
  outputDir: path.resolve(__dirname, "../public/dist/"),

  // Django will hash file names, not webpack
  filenameHashing: false,

  // See: https://vuejs.org/v2/guide/installation.html#Runtime-Compiler-vs-Runtime-only
  runtimeCompiler: true,

  devServer: {
    writeToDisk: true, // Write files to disk in dev mode, so Django can serve the assets
  },

  transpileDependencies: ["vuetify"],
};
