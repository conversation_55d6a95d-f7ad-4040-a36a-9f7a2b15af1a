// require("dotenv").config();
// const sgMail = require("@sendgrid/mail");

// const apikey =
//   "*********************************************************************";

// sgMail.setApiKey(apikey);

// module.exports = sendEmail;

// async function sendEmail({
//   to,
//   subject,
//   from = "<EMAIL>",
 
//   ...rest
// }) {
//   try {
//     const msg = {
//       to,
//       subject,
//       from,
//       ...rest,
//     };

//     console.log({
//       apikey,
//       p: process.env.sendgrid_key,
//     });

//     await sgMail.send(msg);
//   } catch (error) {
//     console.log({ error }, "kwa mail");
//     throw error;
//   }
// }



const axios = require("axios");

const email = {
  loadApi: () => {
    axios.defaults.baseURL = process.env.EMAIL_API_URL;
    axios.defaults.headers.common["apiKey"] = process.env.EMAIL_API_KEY;
    return axios;
  },
  sendEmail: async ({
    to,
    subject,
    emailBody,
    tenant = "platinumKenya",
    cc = "",
    attachments = [],
  }) => {
    const emailSent = await email.loadApi().post("/email", {
      to,
      subject,
      body: emailBody,
      fromName: "Collections Team",
      tenantId: tenant,
      groupRecordId: "pkeUserAccess",
      attachments,
      cc
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return emailSent;
  },
};

module.exports = email;
