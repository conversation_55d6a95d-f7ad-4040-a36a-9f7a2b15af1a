<template>
  <v-container> <p></p></v-container>
</template>

<script>
// @ is an alias to /src
import DemandLetterService from "@/services/proclamationdemand.service.js";
export default {
  name: "Home",
  components: {},
  mounted() {
    DemandLetterService.demandLetter()
      .then(({ message }) => {
        this.$store.dispatch("toggleModal", message);
      })
      .catch(() => {
        console.log("err here");
        this.$store.dispatch(
          "toggleModal",
          "oops, something went wrong, retrying..."
        );
        return DemandLetterService.demandLetter();
      })
      .then(({ message }) => {
        this.$store.dispatch("toggleModal", message.message);
      })
  },
};
</script>

