const email = require("../utils/email");
const fs = require("fs");

class MailService {
  constructor() {}

  async _sendReportEmail(
    pathToAttachment,
    clientEmail,
    firstName,
    noticeType,
    vehicleRegistrationNumber,
    accountID
  ) {
    if (!clientEmail && process.env.EMAIL_SETTING !== "local") {
      return {
        emailSent: false,
        emailData: {
          errorReason: "Client email is missing",
          errorSource: "MailService",
        },
      };
    }

    let fileName = "";
    let message = [];
    let subject = "";
    let ccList = [];

    const attachment = fs.readFileSync(pathToAttachment).toString("base64");

    // Log attachment size for debugging
    console.log(`Attachment size: ${attachment.length} characters (base64), approximately ${Math.round(attachment.length * 0.75)} bytes`);

    try {
      if (noticeType === "Demand Notice" || noticeType === "Final Notice") {
        fileName = noticeType;
        subject = noticeType;
        message = [
          `Dear ${firstName},`,
          "",
          `Please find attached file for your ${noticeType}.`,
          "",
          "Best regards,",
          "Collections Team",
        ];
      } else if (noticeType === "Proclamation Notice") {
        fileName = "Proclamation Collection Notice";
        subject = `Proclamation/${firstName}/${vehicleRegistrationNumber}/${accountID}`;
        message = [
          `Dear ANTIQUE,`,
          "",
          `Kindly find the attached instructions to immediately issue a proclamation notice for to the debtor captioned therein.`,
          "",
          "Do not hesitate to contact us if you need any further clarification.",
          "",
          "Best regards,",
          "Collections Team",
        ];

        // Only include CC list for Proclamation Notice
        ccList = [
          {
            email: "<EMAIL>",
          },
          {
            email: "<EMAIL>",
          },
        ];
      }

      let toList = [
        {
          email:
            process.env.EMAIL_SETTING == "local"
              ? "<EMAIL>"
              : clientEmail,
        },
      ];

      if (noticeType === "Proclamation Notice") {
        toList = [
          {
            email: "<EMAIL>", // Send to this email address only for Proclamation Notice
          },
        ];
      }

      let data = {
        to: toList,
        subject: subject,
        text: message.join("\n"),
        attachments: [
          {
            content: attachment,
            filename: fileName,
            type: "application/pdf",
            disposition: "attachment",
          },
        ],
      };

      if (ccList.length > 0 && noticeType === "Proclamation Notice") {
        data.cc = ccList; // Include CC list only for Proclamation Notice
      }

      // Format data for external email API
      const emailData = {
        to: data.to.map(recipient => recipient.email), // Extract email addresses from objects
        subject: data.subject,
        emailBody: data.text,
        attachments: data.attachments.map(attachment => ({
          content: attachment.content,
          filename: attachment.filename,
          type: attachment.type
        }))
      };

      // Add CC if present
      if (data.cc && data.cc.length > 0) {
        emailData.cc = data.cc.map(recipient => recipient.email);
      }

      console.log('Sending email with data:', JSON.stringify({
        to: emailData.to,
        subject: emailData.subject,
        attachmentCount: emailData.attachments.length,
        hasCC: !!emailData.cc
      }, null, 2));

      const resp = await email.sendEmail(emailData);
      return {
        emailSent: true,
        emailData: resp,
      };
    } catch (error) {
      console.log(JSON.stringify(error, null, 3));

      const errorDetails = {
        errorReason:
          error?.response?.body?.errors[0]?.message ||
          error.message ||
          "Unknown error",
        errorSource: "sendgrid",
        clientEmail: clientEmail,
      };
      return {
        emailSent: false,
        emailData: errorDetails,
      };
    }
  }
}

module.exports = new MailService();
