// require("dotenv").config();

// const config = {
//   platinumkenya: {
//     sandbox: {
//       baseUrl: "https://platinumkenya.sandbox.mambu.com/api",
//       apiKey: process.env.mambu_platinumkenya_sandbox_api_key,
//     },
//     production: {
//       apiKey: process.env.mambu_platinumkenya_production_api_key,
//       baseUrl: "https://platinumkenya.mambu.com/api",
//     },
//   },
// };


// /**
//  * @typedef {object} Config
//  * @property {string} baseUrl - The mambu endpoint
//  * @property {string} apiKey- The mambu apikey
//  * 
//  * /
//  /** 
//  * @param {object} param
//  * @param {string} param.sub  - platinumkenya
//  * @param {string} param.mambu_env - could be sandbox/production
//  * @returns {Config}
//  * 
//  */
// function getConfig({ sub = "platinumkenya", mambu_env = "production" }) {
//   const val = {
//     ...config[sub][mambu_env],
//   };
//   return val;
// }

// module.exports = getConfig;





require("dotenv").config();

const config = {
  platinumkenya: {
    sandbox: {
      baseUrl: "https://platinumkenya.sandbox.mambu.com/api",
      apiKey: process.env.mambu_platinumkenya_sandbox_api_key,
    },
    production: {
      apiKey: process.env.mambu_platinumkenya_production_api_key,
      baseUrl: "https://platinumkenya.mambu.com/api",
    },
  },
};



/**
 * @typedef {object} Config
 * @property {string} baseUrl - The mambu endpoint
 * @property {string} apiKey- The mambu apikey
 * 
 * /
 * 
 /** 
 * @param {object} param
 * @param {string} param.sub  - platinumkenya
 * @param {string} param.mambu_env - could be sandbox/production
 * @returns {Config}
 * 
 */

function getConfig({ sub = "platinumkenya", mambu_env = "production" }) {
  const val = {
    ...config[sub][mambu_env],
  };

  return val;
}

module.exports = getConfig;
