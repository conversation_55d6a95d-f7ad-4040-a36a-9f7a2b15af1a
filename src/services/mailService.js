const email = require("../utils/email");
const fs = require("fs");

class MailService {
  constructor() {}

  async _sendReportEmail(
    pathToAttachment,
    clientEmail,
    firstName,
    noticeType,
    vehicleRegistrationNumber,
    accountID
  ) {
    if (!clientEmail && process.env.EMAIL_SETTING !== "local") {
      return {
        emailSent: false,
        emailData: {
          errorReason: "Client email is missing",
          errorSource: "MailService",
        },
      };
    }

    let fileName = "";
    let message = [];
    let subject = "";
    let ccList = [];

    const attachment = fs.readFileSync(pathToAttachment).toString("base64");

    try {
      if (noticeType === "Demand Notice" || noticeType === "Final Notice") {
        fileName = noticeType;
        subject = noticeType;
        message = [
          `Dear ${firstName},`,
          "",
          `Please find attached file for your ${noticeType}.`,
          "",
          "Best regards,",
          "Collections Team",
        ];
      } else if (noticeType === "Proclamation Notice") {
        fileName = "Proclamation Collection Notice";
        subject = `Proclamation/${firstName}/${vehicleRegistrationNumber}/${accountID}`;
        message = [
          `Dear ANTIQUE,`,
          "",
          `Kindly find the attached instructions to immediately issue a proclamation notice for to the debtor captioned therein.`,
          "",
          "Do not hesitate to contact us if you need any further clarification.",
          "",
          "Best regards,",
          "Collections Team",
        ];

        // Only include CC list for Proclamation Notice
        ccList = [
          {
            email: "<EMAIL>",
          },
          {
            email: "<EMAIL>",
          },
        ];
      }

      let toList = [
        {
          email:
            process.env.EMAIL_SETTING == "local"
              ? "<EMAIL>"
              : clientEmail,
        },
      ];

      if (noticeType === "Proclamation Notice") {
        toList = [
          {
            email: "<EMAIL>", // Send to this email address only for Proclamation Notice
          },
        ];
      }

      
      let data = {
        to: toList,
        subject: subject,
        text: message.join("\n"), // Join the message array with newline separator
        attachments: [
          {
            content: attachment,
            filename: fileName,
            type: "application/pdf",
            disposition: "attachment",
          },
        ],
      };

      if (ccList.length > 0 && noticeType === "Proclamation Notice") {
        data.cc = ccList; // Include CC list only for Proclamation Notice
      }

      const resp = await email.sendEmail({
        to: data.to,
        subject: data.subject,
        emailBody: data.text,
        attachments: data.attachments
      });
      return {
        emailSent: true,
        emailData: resp,
      };
    } catch (error) {
      console.log(JSON.stringify(error, null, 3));

      const errorDetails = {
        errorReason:
          error?.response?.body?.errors[0]?.message ||
          error.message ||
          "Unknown error",
        errorSource: "sendgrid",
        clientEmail: clientEmail,
      };
      return {
        emailSent: false,
        emailData: errorDetails,
      };
    }
  }
}

module.exports = new MailService();
