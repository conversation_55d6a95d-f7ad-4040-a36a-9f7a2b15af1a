import Vue from "vue";
import VueRouter from "vue-router";
import Compute from "../views/Compute.vue";
import Reports from "../views/Reports.vue";

Vue.use(VueRouter);

const routes = [
  {
    path: "/Demandletternotice",
    name: "Demandletternotice",
    component: Reports,
  },

  {
    path: "/",
    redirect: "/Demandletternotice",
  },

  {
    path: "/compute",
    name: "compute",
    component: Compute,
  },
  {
    path: "/about",
    name: "About",
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: () =>
      import(/* webpackChunkName: "about" */ "../views/About.vue"),
  },
];

const router = new VueRouter({
  routes,
});

export default router;
