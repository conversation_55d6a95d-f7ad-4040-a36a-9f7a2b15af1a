<template>
  <v-data-table
    :headers="headers"
    :items="reportdata"
    sort-by="calories"
    class="elevation-1"
    :loading="loading"
    :loading-text="loading_text"
    v-if="!hideFilesTables"
    :footer-props="{
      'items-per-page-options': [10, 20, 30, 40, 50, 100],
    }"
    :items-per-page="30"
    :search="search"
  >
    <template v-slot:top>
      <v-toolbar flat>
        <v-toolbar-title>Files Reports</v-toolbar-title>
        <v-divider class="mx-4" inset vertical></v-divider>
        <v-btn icon @click.prevent="fetchReports">
          <v-icon color="primary">mdi-file-refresh</v-icon>
        </v-btn>
        <v-spacer></v-spacer>
        <!-- <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field> -->

        <div class="export">
          <v-btn class="ma-2 export-btn" color="primary" dark>
            <export-excel
              :data="reportdata"
              name="DEMAND LETTER Reports.xls"
              :fields="reportHeader"
              worksheet="demand Letter Reports"
            >
              Download Reports <v-icon dark> mdi-cloud-download </v-icon>
            </export-excel>
          </v-btn>
        </div>
      </v-toolbar>
    </template>

    <template v-slot:no-data>
      <v-btn color="primary" @click="fetchReports"> Refresh </v-btn>
    </template>

    <template v-slot:[`item.createdAt`]="{ item }">{{
      formatDate(item.createdAt)
    }}</template>

    <template v-slot:[`item.updatedAt`]="{ item }">{{
      formatDate(item.updatedAt)
    }}</template>
  </v-data-table>
</template>
  
<script>
import UploadService from "@/services/UploadService.js";

export default {
  name: "ReportsTable",
  components: {},

  mounted() {
    this.fetchReports();
  },
  computed: {
    reports() {
      return this.$store.state.reports;
    },
  },

  data: () => ({
    dialog: false,
    loading: false,
    loading_text: "Loading... Please wait",
    reportdata: [],
    reportHeader: {
      "Loan Id": "accountId",
      "Date Report Generated": "createdAt",
      status: "status",
      Email_attached: "email_attached",
      errorReason: "errorReason",
      Environment: "environment",
      Subsidiary: "sub",
    },
    hideFilesTables: false,
    dialogDelete: false,
    search: "",
    headers: [
      {
        text: "id",
        align: "start",
        sortable: false,
        value: "id",
      },
      { text: "Loan Id", value: "accountId" },
      { text: "Date Report Generated", value: "createdAt" },
      { text: "status", value: "status" },
      { text: "Email_attached", value: "email_attached" },
      { text: "errorReason", value: "errorReason" },
      { text: "Environment", value: "environment", sortable: false },
      { text: "Subsidiary", value: "sub", sortable: false },
    ],
  }),

  methods: {
    fetchReports() {
      return UploadService.getReports().then((data) => {
        console.log({
          data,
        });
        this.reportdata = data;
      });
    },
    formatDate(date) {
      var strDate = date;
      return strDate.substring(0, 10);
    },
  },
};
</script>
  
  
  

