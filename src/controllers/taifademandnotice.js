const ClientService = require("../services/clientService");
const { insertData } = require("../services/db");
const { selectData } = require("../services/db");
const { } = require('sequelize');
const MailService = require("../services/mailService");
const fs = require('fs/promises')
const dd = require("../utils/dd");
const seconddd = require("../utils/secondDemand");
const finaldd = require("../utils/finalDemand");
const pdfMake = require("../utils/pdf");
const getConfig = require("../utils/config");
const extractMambuUser = require('../utils/extractMambuUser')

const branchUsers = require("../resources/agentManagers.json");

const controller = {};

async function getBranchAndCreditOfficer(loanAccount, config) {
  try {
    const assignedBranchKey = loanAccount.assignedBranchKey;
    const assignedUserKey = loanAccount.assignedUserKey;
    if (!assignedBranchKey || !assignedUserKey) {
      throw new Error("Assigned branch or assigned user key not found in loanAccount");
    }

    const clientService = new ClientService(config);

    // Fetch branch information
    const branchInfo = await clientService.getBranch(assignedBranchKey);
    const branchName = branchInfo.name;

    // Fetch credit officer information
    const creditOfficerInfo = await clientService.getUser(assignedUserKey);
    const creditOfficerName = `${creditOfficerInfo.firstName} ${creditOfficerInfo.lastName}`;
    console.log("creditOfficerInfo:", creditOfficerInfo);

    return {
      branchName,
      creditOfficerName,
    };
  } catch (error) {
    console.error("Error while getting branch and credit officer details:", error.message);
    return null;
  }
}

// Filter function to get users based on branch name and role
function filterAndMapUsers(branchName) {
  return branchUsers
    .filter((user) => user.Branch === branchName && (user.Role === "Tavern  Development Agent" || user.Role === "Team Leader" || user.Role === "Development Agents") && user.Email !== '')
    .map((user) => ({ email: user.Email }));
}

/**
 * this method will be used by webhook and ui
 * for ui it will send the signed request which will result in extraction of loanID from the base64, the rest of the data will be passed by the auth middleware
 *
 * for the webhook, we will require this payload below so as to by pass the auth middleware
 * @param {object} body - req.body
 * @param {string} loanID
 * @param {string} mambu_env - sandbox
 * @param {string} sub - platinumkenya
 * @param {string} source - mambu
 *
 */

controller.taifademandnotice = (app) => async function (req, res, next) {
  console.log(req.body);

  let mambuObj = {}
  if (req.headers.mambuuser) {
    mambuObj = extractMambuUser(req.headers.mambuuser);
  }

  const loanID = req.body.loanId || mambuObj.loanId
  // const loanID = req.loanID || req.body.loanID;

  let kind = req.body?.kind ?? "first";
  console.log("loanID", loanID);
  const ReportModel = app.get('sequelize').models.report;

  const { mambu_env = "production", sub = "platinumkenya" } = req?.mambuData || {
    // base_url: "https://platinumkenya.mambu.com",
    mambu_env: "production",
    sub: "platinumkenya",
    source: "ui",
  };

  const config = getConfig({
    mambu_env,
    sub,
  });

  console.log({
    config,
    mambu_env,
    sub
  })

  if (!loanID) {
    return res.status(400).json({ message: "LoanID must be provided " });
  }

  let pathToAttachment = null;

  try {

    const loanAccount = await new ClientService(config).getLoanById(loanID);

    const vehicleRegNo = loanAccount.VEHICLE_INFORMATION_Loan_Account.RN01;

    // const auctioneer = loanAccount._ptfl_mgt.auctioneer_;
    // console.log(auctioneer, "auctioneer"); // This will output the value of the auctioneer custom field

    const auctioneer = loanAccount?._ptfl_mgt?.auctioneer_ ?? "";

    const makeModel = loanAccount.VEHICLE_INFORMATION_Loan_Account.M01;

    console.log("Vehicle Registration Number:", vehicleRegNo);
    console.log("Make Model:", makeModel);

    // Get branch name and credit officer using the getBranchAndCreditOfficer function
    const { branchName, creditOfficerName } = await getBranchAndCreditOfficer(loanAccount, config);
    console.log("Branch Name:", branchName);
    console.log("Credit Officer:", creditOfficerName);

    const date = new Date();
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear().toString();
    const formattedDate = `${day}/${month}/${year}`;
    console.log(formattedDate);


    const payoffBalance = loanAccount._LD001 ? loanAccount._LD001.PB09 || '' : '';
    console.log("payoffBalance", payoffBalance);


    // Call the getClientById function to get the client object
    const client = await new ClientService(config).getClientById(loanAccount.accountHolderKey);

    // Extract the securityInfo from the client object using the updated property name
    const securityInfo = client._Tavern_Security__Clients;

    // Check if securityInfo exists and extract the required properties
    if (securityInfo) {
      const TIT03 = securityInfo.TIT03 || '';
      const TTID02 = securityInfo.TTID02 || '';
      const TID001 = securityInfo.TID001 || '';
      const VT03 = securityInfo.VT03 || '';
      const MMI3D01 = securityInfo.MMI3D01 || '';
      const VAL201 = securityInfo.VAL201 || '';
      const MMI2D01 = securityInfo.MMI2D01 || '';
      const IDT01 = securityInfo.IDT01 || '';
      const IDT013 = securityInfo.IDT013 || '';

      // Now you can use these variables as needed
      // For example:
      console.log(' TIT03:', TIT03);
      console.log('TTID02:', TTID02);
      console.log('TID001:', TID001);

    } else {
      console.log('Security information not found.');
    }

    // Accessing the addresses array from the client object
    const addresses = client.addresses;
    let fullAddress = ''

    // Check if the addresses array exists and has at least one address
    if (addresses && addresses.length > 0) {
      // Fetching the first address (assuming there is only one address in the array)
      const address = addresses[0];

      // Create the desired address string
      fullAddress = `${address.line1}, ${address.city}, ${address.region}, ${address.postcode}, ${address.country}`;

      console.log(fullAddress);

      // Output: "49 Abidjan St, Tembisa, Gauteng, 1632, South Africa"
      // Add the fullAddress to the payload

    } else {
      console.log('No address found.');
    }

    const {
      _ads001,
      encodedKey,
      id,
      activationDate,
      firstName,
      middleName,
      lastName,
      mobilePhone,
      emailAddress = null,
    } = await new ClientService(config).getClientById(
      loanAccount.accountHolderKey
    );

    let totalDue =
      loanAccount.balances.principalDue +
      loanAccount.balances.interestDue +
      loanAccount.balances.feesDue +
      loanAccount.balances.penaltyDue;

    // console.log("totalDue", totalDue);

    if (loanAccount.accountState == "ACTIVE_IN_ARREARS") {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      console.log("Current year:", currentYear);

      const activation_Date = new Date(activationDate);
      const formattedActivationDate = activation_Date
        .toISOString()
        .split("T")[0];
      // console.log("activationDate", activation_Date);

      const fullName = `${firstName}${middleName ? " " + middleName : ""
        } ${lastName}`.trim();
      const phoneNumber = mobilePhone;
      const clientAdress = emailAddress;
      const clientName = firstName;


      const payload = {
        loan_account: loanAccount.id,
        loan_amount: loanAccount.loanAmount.toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        Total_Due: totalDue.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","),
        product_name: loanAccount.loanName,
        current_year: currentYear,
        full_name: fullName,
        firstName: clientName,
        Phone_Number: phoneNumber,
        Client_Adress: clientAdress,
        securityInfo: securityInfo && securityInfo.TIT03 ? securityInfo.TIT03 : "",
        securityInfo2: securityInfo && securityInfo.I2D01 ? securityInfo.I2D01 : "",
        securityInfo3: securityInfo && securityInfo.TTID02 ? securityInfo.TTID02 : "",
        securityInfo4: securityInfo && securityInfo.MMI3D01 ? securityInfo.MMI3D01 : "",
        securityInfo5: securityInfo && securityInfo.TID001 ? securityInfo.TID001 : "",
        securityInfo6: securityInfo && securityInfo.IDT013 ? securityInfo.IDT013 : "",
        Postal_Address: _ads001 ? _ads001["POSTAL001"] : "",
        client_id: id,
        activation_date: formattedActivationDate,
        formattedDate: formattedDate,
        fullAddress: fullAddress,
        payoffBalance: payoffBalance || "",
        vehicle_registration_number: loanAccount.VEHICLE_INFORMATION_Loan_Account.RN01 || "",
        // Auctioneer_Name: loanAccount._ptfl_mgt.auctioneer_ || "",
        Auctioneer_Name: auctioneer,
        make_model: loanAccount.VEHICLE_INFORMATION_Loan_Account.M01 || ""
        //   payoffBalance: payoffBalance.toLocaleString(undefined, {
        //     minimumFractionDigits: 2,
        //     maximumFractionDigits: 2,
        // })

      };

      console.log('payload ', payload);

      const daysInArrears = loanAccount.daysInArrears;
      console.log('daysInArrears ', daysInArrears);

      // Declare totalDue variable
      // let totalDue;

      // Calculate totalDue
      // totalDue =
      //   loanAccount.balances.principalDue +
      //   loanAccount.balances.interestDue +
      //   loanAccount.balances.feesDue +
      //   loanAccount.balances.penaltyDue;
      //   console.log('Total due: ', totalDue);

      // Set the appropriate kind based on the number of days in arrears or total due
      if (daysInArrears === 3) {
        kind = "first"; // Use "first" demand letter when daysInArrears is 4
      } else if (totalDue >= 15000) {
        kind = "second"; // Use "second" demand letter when total due is greater than or equal to 15000
      } else if (daysInArrears === 11) {
        kind = "final"; // Use "final" demand letter when daysInArrears is 30
      } else {
        kind = "first"; // Default to "final" demand letter for other cases
      }

      console.log('Kind: ', kind);

      // const daysInArrears = loanAccount.daysInArrears;
      // console.log('daysInArrears ', daysInArrears);

      // // Set the appropriate kind based on the number of days in arrears
      // if (daysInArrears === 3) {
      //   kind = "first"; // Use "first" demand letter when daysInArrears is 4
      // } else if (daysInArrears === 8) {
      //   kind = "second"; // Use "second" demand letter when daysInArrears is 8
      // } else if (daysInArrears === 11) {
      //   kind = "final"; // Use "final" demand letter when daysInArrears is 30
      // } else {
      //   kind = "second"; // Default to "first" demand letter for other cases
      // }

      const ccList = filterAndMapUsers(branchName);
      console.log("ccList", ccList);

      // generate html from dd
      let html = null;
      let file_name = "_" + fullName + ".pdf";

      switch (kind) {

        case "first": {
          html = seconddd(payload);
          file_name = "DemandLetter" + file_name
          title = "Demand Notice";
          break;

        }

        case "second": {
          html = dd(payload);
          file_name = "ProclamationCollectionNotice" + file_name
          title = "Proclamation Collection Notice";
          break;
        }

        case "final": {
          html = finaldd(payload)
          file_name = "FinalDemandLetter" + file_name
          title = "Final Demand Notice";
          break;
        }

        default:
          html = dd(payload);
          file_name = "DemandLetter" + file_name;
          title = "Demand Notice";
          break;

      }


      const { pdf_file_path } = await pdfMake(html, file_name, kind);
      console.log({
        pdf_file_path,
      });

      pathToAttachment = pdf_file_path;
      const attachPdf = await new ClientService(config).attachPdf({
        userId: loanID,
        fileName: file_name,
        title: title,
        file_path: pdf_file_path,
      });

      // send email
      const sendEmail = await MailService._sendReportEmail(
        pdf_file_path,
        emailAddress,
        firstName,
        kind === "first" ? "Demand Notice" : kind === "second" ? "Proclamation Notice" : "Final Notice",
        // loanAccount.VEHICLE_INFORMATION_Loan_Account.RN01, // Pass vehicleRegistrationNumber
        loanID ,// Pass the loanID as accountID
        // auctioneer,
        vehicleRegNo,
        ccList,

      );

      let errorMessage = "";
      let statusResult = "attached";
      let email_attached = true;


      if (!sendEmail.emailSent) {
        statusResult = "failed";
        email_attached = false;
      }

      if (!attachPdf.hasValue || !sendEmail.emailSent) {
        errorMessage = !sendEmail.emailSent
          ? sendEmail.emailData.errorReason
          : (!attachPdf.hasValue ? attachPdf.data.errorReason : "");
      }

      const report = await insertData(ReportModel, [{
        accountId: loanID,
        status: statusResult,
        email: emailAddress ?? "",
        errorReason: errorMessage,
        environment: mambu_env,
        sub,
        email_attached
      }
      ]);

      return res.json({
        message: attachPdf?.data?.errorReason ? attachPdf.data.errorReason : "Pdf file attached",
        response: attachPdf,
      });


    } else {
      return res.json({
        message: "Invalid loan account state",
        loanAccount: loanAccount.accountState
      });
    }
  } catch (error) {
    console.log(error)
    next(error);
  } finally {
    if (pathToAttachment) {
      await fs.unlink(pathToAttachment);
    }
  }
};

controller.getReports = function (app) {
  return async function (req, res) {
    try {
      const { report } = app.get('sequelize').models;
      return selectData(report, {}).then(data => {
        return res.send(data);
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ message: error.message });
    }
  };
}

module.exports = controller;









