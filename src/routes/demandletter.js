const express = require("express");
const router = express.Router();
const Auth = require("../_middlewares/auth");

// ----------------------------

const taifaworkingcapital = require("../controllers/taifademandnotice");

module.exports=(app)=>{
    // router.post("/taifacapitalDemand", taifaworkingcapital.taifademandnotice(app));
    router.post("/taifacapitalDemand", Auth,  taifaworkingcapital.taifademandnotice(app));
    router.post("/demandLetter-webhook", function(req, res,next ){
        req.loanID = req.body.loanId;
        req.mambuData = {
          loanID: req.loanID,
          source:"ui"
        };
        return next();
    },   taifaworkingcapital.taifademandnotice(app));
    router.get("/getReports",  taifaworkingcapital.getReports(app));
   
    return router;
}

