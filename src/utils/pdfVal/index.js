const ReportService = require("#services/reportService");
const ClientService = require("#services/clientService");

const getConfig = require("../config");

const pdfVal = {};

pdfVal.insertRecords = async function ({
  records,
  baseUrl,
  sub,
  environment,
  fileId,
  fileName,
}) {
  const mappedRecords = records.map((item) => {
    const [loanAccount, ability] = item;

    let data = {
      loanAccount: loanAccount + "",
      ability: ability + "",
      environment,
      sub,
      baseUrl,
      processed: false,
      fileId,
      fileName,
      status: "received for processing",
    };

    return data;
  });

  return ReportService.bulkInsertReport(mappedRecords);
};

pdfVal.processTask = async function (task) {
  const { loanAccount, ability, id, environment, sub, baseUrl, fileId } = task;
  const config = getConfig({ mambu_env: environment, baseUrl, sub });
  const customInformation = [
    {
      customFieldID: "ao_1",
      value: parseFloat(ability).toFixed(2),
    },
  ];
  const { hasValue, data } = await new ClientService(
    config
  ).updateLoanConversionDetails(loanAccount, customInformation);

  console.log({
    dataProcess: data,
  });

  if (!hasValue) {
    return await pdfVal.updateFileAndReport({
      fileId,
      add: 1,
      reportId: id,
      params: {
        status: "failed",
        processed: true,
        ...data,
      },
    });
  }

  const updateUpdate = await pdfVal.updateFileAndReport({
    fileId,
    add: 1,
    reportId: id,
    params: {
      status: "attached",
      processed: true,
    },
  });

  return "done";
};

pdfVal.updateFileAndReport = async function ({
  fileId,
  add,
  params,
  reportId,
}) {
  const updateFile = ReportService.incrementReportedCount(fileId, add);
  const updateRecord = ReportService.updateReportById(reportId, {
    ...params,
  });

  return Promise.allSettled([updateFile, updateRecord]);
};

module.exports = pdfVal;
