<template>
  <v-app id="inspire">
    <Drawer :drawer.sync="drawer" />
    <v-app-bar app color="primary" dark>
      <v-app-bar-nav-icon @click="toggledrawer"></v-app-bar-nav-icon>

      <v-toolbar-title> Taifa Working Capital Demand Letter</v-toolbar-title>
      <v-btn icon to="/Demandletternotice" class="ml-2">
        <v-icon>mdi-file-upload</v-icon>
      </v-btn>

      <v-btn icon to="/compute" class="ml-2">
        <v-icon>mdi-calculator</v-icon>
      </v-btn>
    </v-app-bar>
    <v-main>
      <v-container fluid>
        <router-view />
        <Loader />
        <Modal />
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
import Loader from "@/components/Loader.vue";
import Modal from "@/components/Modal.vue";
import Drawer from "@/components/Drawer.vue";

export default {
  name: "App",
  components: {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Drawer,
  },
  data: () => ({
    drawer: false,
  }),
  methods: {
    toggledrawer() {
      this.drawer = !this.drawer;
    },
  },
};
</script>



